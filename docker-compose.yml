version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:10
    container_name: kylas_fblead_postgres
    environment:
      POSTGRES_DB: kylas_fblead_development
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  redis_data:
  bundle_cache:
  node_modules:
