<%= render layout:'account_details/shared/tabs_layout', locals: { account: @account_detail, tenant: @account_detail.tenant } do %>
  <div class="usage-reports-container">
    <div class="header-section">
      <h3><%= t('usage_reports.title') %></h3>
      <p class="text-muted"><%= t('usage_reports.description') %></p>
    </div>

    <!-- Report Configuration Form -->
    <div class="report-config-section">
      <div class="card">
        <div class="card-header">
          <h5><%= t('usage_reports.configuration') %></h5>
        </div>
        <div class="card-body">
          <%= form_with url: usage_reports_data_account_detail_path, method: :get,
                        local: false, id: 'usage-report-form', class: 'row g-3' do |form| %>
            
            <!-- Report Type Selection -->
            <div class="col-md-4">
              <%= form.label :report_type, t('usage_reports.report_type'), class: 'form-label' %>
              <%= form.select :report_type, options_for_select(@report_types), 
                              { prompt: t('usage_reports.select_report_type') }, 
                              { class: 'form-select', required: true } %>
            </div>

            <!-- Comparison Type Selection -->
            <div class="col-md-4">
              <%= form.label :comparison_type, t('usage_reports.comparison_type'), class: 'form-label' %>
              <%= form.select :comparison_type, options_for_select(@comparison_types), 
                              { selected: 'none' }, 
                              { class: 'form-select' } %>
            </div>

            <!-- Date Range Selection -->
            <div class="col-md-4">
              <%= form.label :date_range, t('usage_reports.date_range'), class: 'form-label' %>
              <%= form.select :date_range, options_for_select(@date_ranges), 
                              { selected: '30_days' }, 
                              { class: 'form-select' } %>
            </div>

            <!-- Custom Date Range (Hidden by default) -->
            <div class="col-md-6" id="custom-date-range" style="display: none;">
              <%= form.label :start_date, t('usage_reports.start_date'), class: 'form-label' %>
              <%= form.date_field :start_date, class: 'form-control' %>
            </div>

            <div class="col-md-6" id="custom-date-range-end" style="display: none;">
              <%= form.label :end_date, t('usage_reports.end_date'), class: 'form-label' %>
              <%= form.date_field :end_date, class: 'form-control' %>
            </div>

            <!-- Action Buttons -->
            <div class="col-12">
              <div class="btn-group" role="group">
                <%= form.submit t('usage_reports.generate_report'), 
                                class: 'btn btn-primary', 
                                id: 'generate-report-btn' %>
                
                <button type="button" class="btn btn-success" id="export-csv-btn" disabled>
                  <i class="fa fa-download"></i> <%= t('usage_reports.export_csv') %>
                </button>
                
                <button type="button" class="btn btn-secondary" id="clear-report-btn">
                  <%= t('usage_reports.clear_report') %>
                </button>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="text-center" style="display: none;">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2"><%= t('usage_reports.generating_report') %></p>
    </div>

    <!-- Report Results Container -->
    <div id="report-results" class="report-results-section" style="display: none;">
      <!-- Report data will be loaded here via AJAX -->
    </div>

    <!-- Error Container -->
    <div id="error-container" class="alert alert-danger" style="display: none;">
      <!-- Error messages will be displayed here -->
    </div>
  </div>

  <!-- Chart.js for visualizations -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('usage-report-form');
      const loadingIndicator = document.getElementById('loading-indicator');
      const reportResults = document.getElementById('report-results');
      const errorContainer = document.getElementById('error-container');
      const exportBtn = document.getElementById('export-csv-btn');
      const clearBtn = document.getElementById('clear-report-btn');
      const dateRangeSelect = document.getElementById('usage_report_date_range');
      const comparisonTypeSelect = document.getElementById('usage_report_comparison_type');
      const customDateRange = document.getElementById('custom-date-range');
      const customDateRangeEnd = document.getElementById('custom-date-range-end');

      // Show/hide custom date range fields
      function toggleCustomDateRange() {
        const showCustom = dateRangeSelect.value === 'custom' || comparisonTypeSelect.value === 'custom';
        customDateRange.style.display = showCustom ? 'block' : 'none';
        customDateRangeEnd.style.display = showCustom ? 'block' : 'none';
      }

      dateRangeSelect.addEventListener('change', toggleCustomDateRange);
      comparisonTypeSelect.addEventListener('change', toggleCustomDateRange);

      // Form submission
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading, hide results and errors
        loadingIndicator.style.display = 'block';
        reportResults.style.display = 'none';
        errorContainer.style.display = 'none';
        exportBtn.disabled = true;

        // Submit form via fetch
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);

        fetch(form.action + '?' + params.toString(), {
          method: 'GET',
          headers: {
            'Accept': 'text/html',
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.text();
        })
        .then(html => {
          loadingIndicator.style.display = 'none';
          reportResults.innerHTML = html;
          reportResults.style.display = 'block';
          exportBtn.disabled = false;
        })
        .catch(error => {
          loadingIndicator.style.display = 'none';
          errorContainer.innerHTML = '<%= t("usage_reports.error_generating_report") %>';
          errorContainer.style.display = 'block';
          console.error('Error:', error);
        });
      });

      // Export CSV
      exportBtn.addEventListener('click', function() {
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);
        params.append('format', 'csv');
        
        window.location.href = '<%= usage_reports_export_account_detail_path %>' + '?' + params.toString();
      });

      // Clear report
      clearBtn.addEventListener('click', function() {
        reportResults.style.display = 'none';
        errorContainer.style.display = 'none';
        exportBtn.disabled = true;
        form.reset();
        toggleCustomDateRange();
      });
    });
  </script>

  <style>
    .usage-reports-container {
      padding: 20px 0;
    }

    .header-section {
      margin-bottom: 30px;
    }

    .report-config-section {
      margin-bottom: 30px;
    }

    .report-results-section {
      margin-top: 30px;
    }

    .chart-container {
      position: relative;
      height: 400px;
      margin: 20px 0;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }

    .metric-card {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
    }

    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #007bff;
    }

    .metric-label {
      font-size: 0.9rem;
      color: #6c757d;
      margin-top: 5px;
    }

    .comparison-indicator {
      font-size: 0.8rem;
      margin-top: 5px;
    }

    .comparison-positive {
      color: #28a745;
    }

    .comparison-negative {
      color: #dc3545;
    }

    .comparison-neutral {
      color: #6c757d;
    }
  </style>
<% end %>
