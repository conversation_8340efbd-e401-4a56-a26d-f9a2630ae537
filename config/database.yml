# PostgreSQL. Versions 9.3 and up are supported.
#
# Install the pg driver:
#   gem install pg
# On macOS with Homebrew:
#   gem install pg -- --with-pg-config=/usr/local/bin/pg_config
# On macOS with MacPorts:
#   gem install pg -- --with-pg-config=/opt/local/lib/postgresql84/bin/pg_config
# On Windows:
#   gem install pg
#       Choose the win32 build.
#       Install PostgreSQL and put its /bin directory on your path.
#
# Configure Using Gemfile
# gem "pg"
#
# PostgreSQL configuration for all environments
# Uses Docker Compose PostgreSQL containers by default
default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  username: <%= ENV.fetch("DATABASE_USERNAME") { "postgres" } %>
  password: <%= ENV.fetch("DATABASE_PASSWORD") { "postgres" } %>
  host: <%= ENV.fetch("DATABASE_HOST") { "localhost" } %>
  port: <%= ENV.fetch("DATABASE_PORT") { 5432 } %>
  timeout: 5000

development:
  <<: *default
  database: kylas_customer_success_development
  # Docker Compose PostgreSQL container uses default port 5432

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: kylas_customer_success_test
  port: <%= ENV.fetch("TEST_DATABASE_PORT") { 5433 } %>
  # Docker Compose test PostgreSQL container uses port 5433

# As with config/credentials.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password or a full connection URL as an environment
# variable when you boot the app. For example:
#
#   DATABASE_URL="postgres://myuser:mypass@localhost/somedatabase"
#
# If the connection URL is provided in the special DATABASE_URL environment
# variable, Rails will automatically merge its configuration values on top of
# the values provided in this file. Alternatively, you can specify a connection
# URL environment variable explicitly:
#
#   production:
#     url: <%= ENV["MY_APP_DATABASE_URL"] %>
#
# Read https://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full overview on how database connection configuration can be specified.
#
production:
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  database: kylas_customer_success_production
  username: <%= Rails.application.credentials.dig(:database, :username) || 'postgres' %>
  password: <%= Rails.application.credentials.dig(:database, :password) || 'postgres' %>
  host: <%= Rails.application.credentials.dig(:database, :host) || 'localhost' %>
  port: <%= Rails.application.credentials.dig(:database, :port) || 5432 %>

staging:
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 15 } %>
  database: kylas_customer_success_staging
  username: <%= Rails.application.credentials.dig(:database, :username) || 'postgres' %>
  password: <%= Rails.application.credentials.dig(:database, :password) || 'postgres' %>
  host: localhost
