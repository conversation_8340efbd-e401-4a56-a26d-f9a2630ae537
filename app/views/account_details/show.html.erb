 <%= render layout:'account_details/shared/tabs_layout', locals: { account: @account, tenant: @account.tenant } do %>
  <div class="account-info">
    <div class="basic-info">
      <span><strong style='margin-inline:5px' ><%= t('account_details.basic_information')%></strong></span>
      <div>
        <span><strong class="basic-info-line" ><%= t('account_details.account_name')%></strong>
          <%= @account.tenant&.name.nil? ? "-" : @account.tenant.name %>
        </span>
        <span><strong class="basic-info-line" ><%= t('account_details.tenant_id')%></strong>
          <%= @account.tenant&.id.nil? ? "-" : @account.kylas_tenant_id %>
        </span>
        <span><strong class="basic-info-line" ><%= t('account_details.industry')%></strong>
          <%= @account.industry&.titleize.nil? ? "-" : @account.industry&.titleize %>
        </span>
      </div>
    </div>
    <div class="vl" ></div>
    <div class="contact-info">
    <span><strong><%= t('account_details.contact_details')%></strong></span>
      <div>
        <span>
            <i class="fa fa-envelope"></i>
            <a class="email-link" href="mailto:<%=@account.email%>">
              <%= @account.email.presence || '-' %>
            </a>
        </span>
        <span>
          <i class="fa fa-phone"></i>
          <a class="phone-link" href="<%@account.mobile.nil? ?  "" : "tel:" %><%=@account.mobile%>">
            <%= @account.mobile.presence || '-' %>
          </a>
        </span>
      </div>
    </div>
  </div>
  <div class="account-form">
    <span><%= t('account_details.account_form_instructions')%></span>
    <%= form_with url: account_detail_url(@account), method: :patch do |form| %>
      <%= form.label :account_manager_id, t('account_details.form.account_manager'), {style:"font-weight:600;"} %>
      <%= form.select(:account_manager_id, [], {}, { class: "select select-am", style: "width:300px;"}) %>
      <%= form.label :support_executive_id, t('account_details.form.support_executive'), {style:"font-weight:600;"} %>
      <%= form.select(:support_executive_id, [], {}, { class: "select select-se", style: "width:300px;"}) %>
  </div>
  <div class="marketplace-apps">
    <div><%= t('account_details.marketplace_apps_installed')%></div>
      <% if @account.marketplace_apps_installed.empty? %>
      -
      <% else %>
        <div class="word-container">
          <% @account.marketplace_apps_installed.each_with_index do |word,index| %>
            <div class="word">
              <%=index+1%><span>)</span> <%= word.titleize %>
            </div>
          <% end %>
        </div>
      <% end %>
  </div>
  <div class="usage">
    <div><%= t('account_details.usage')%></div>
        <div class="word-container">
          <% @account.serializable_hash.reject { |key,| USAGE_EXCLUDED_KEYS.include? key }.map do |key,value| %>
            <div class="word">
              <%= key.titleize %> : <%= value.presence || '-' %>
            </div>
          <% end %>
        </div>
  </div>
  <div class="account-status">
    <span class="row">
      <span>
        <%= t('account_details.last_updated_by')%>
      </span>
      <br/>
      <span>
        <%= get_last_updated_by(@account) %>
      </span>
    </span>
    <span class="row">
      <span>
      <%= t('account_details.last_updated_at')%>
      </span>
      <br/>
      <span class="updated-at">
      <%= @account.last_updated_at.presence || '-' %>
      </span>
    </span>
    <span class="row">
      <span>
      <%= t('account_details.system_updated_at')%>
      </span>
      <br/>
      <span class="updated-at">
      <%= @account.tenant.system_updated_at.presence || '-' %>
      </span>
    </span>
  </div>
  <div class="button account-actions">
      <%= form.submit t('account_details.form.save'), class: "btn save-btn", data: { disable_with: t('account_details.form.saving') } %>
    <% end %>
  </div>
<% end %>
<script>
$(document).ready(function() {
  var account_manager = "<%= default_account_manager_name(@account) %>"
  var support_executive = "<%= default_support_executive_name(@account) %>"
  if(account_manager){
    $(".select-am").append(new Option(account_manager, parseInt("<%= raw @account.account_manager_id%>"), true, true));
  };
  if(support_executive){
    $(".select-se").append(new Option(support_executive, parseInt("<%= raw @account.support_executive_id%>"),true, true));
  }
})
$(".select").select2({
  placeholder: '<%= t('form.placeholder.search_in_users') %>',
  minimumInputLength: 3,
  allowClear: true,
  delay:250,
  ajax: {
    url: '<%= list_users_url %>',
    dataType:'json',
    data: function (params) {
      var query = {
        key: params.term,
      }
      return query;
    },
    processResults: function (data) {
      result = data.map((user)=>{
        return { id: user["id"], text: `${user["name"]} (${user["email"]})` }
      })
      return {
        results: result
      };
    }
  }
})
  $('.updated-at').each(function() {
    $(this).text( `${moment($(this).text()).local().format('lll')}` != 'Invalid date' ? `${moment($(this).text()).local().format('lll')}` : "-")
  });
</script>
