# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   "true": "foo"
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  app:
    title: 'Kylas Customer Success'
    brand: 'Kylas'
    name: 'Support'
    sidenav:
      dashboard: "Dashboard"
      accounts: "Accounts"
      manage_users: "Manage Users"
      reports: "Reports"
  danger:
    email_exists: 'User with given Email already exists'
    not_authorized: 'You are not authorized'
    deactivated_account: 'User exists but the account is deactivated'
    invalid_data: 'User data is invalid'
    user_not_exists: 'User does not exist'
    account_not_exists: 'Account does not exist'
    plan_detail_not_exists: 'Plan detail does not exist'
    account_not_updated: 'Account details not updated due to: %{error_message}'
  success:
    invited: 'User invited successfully'
    name_changed: "Name Changed Successfully"
    email_changed: "Email Changed Successfully"
    user_deactivated: 'User Deactivated Successfully'
    user_activated: 'User Activated Successfully'
    account_details_updated: 'Account Details Updated Successfully'
    plan_details_updated: 'Plan Details Updated Successfully'
  alert:
    invitation_already_accepted: 'User has already accepted the invitation'
  usage_reports:
    title: 'Usage Reports'
    description: 'Generate comprehensive usage reports and analytics for this tenant'
    configuration: 'Report Configuration'
    report_type: 'Report Type'
    comparison_type: 'Comparison Type'
    date_range: 'Date Range'
    start_date: 'Start Date'
    end_date: 'End Date'
    select_report_type: 'Select Report Type'
    generate_report: 'Generate Report'
    export_csv: 'Export CSV'
    clear_report: 'Clear Report'
    generating_report: 'Generating report...'
    error_generating_report: 'Error generating report. Please try again.'
    period_from_to: 'Period: %{start} to %{end}'
    comparison_report: 'Comparison Report'
    generated_at: 'Generated at'
    period_1: 'Period 1'
    period_2: 'Period 2'
    total: 'Total'
    average_per_day: 'Average per Day'
    maximum: 'Maximum'
    minimum: 'Minimum'
    date: 'Date'
    metric: 'Metric'
    value: 'Value'
    change: 'Change'
    percentage_change: '% Change'
    period_1_total: 'Period 1 Total'
    period_2_total: 'Period 2 Total'
    comparison: 'Comparison'
    types:
      daily_logged_in_users: 'Daily Logged In Users'
      daily_lead_creation: 'Daily Lead Creation'
      daily_deal_creation: 'Daily Deal Creation'
      daily_contact_creation: 'Daily Contact Creation'
      daily_task_creation: 'Daily Task Creation'
      daily_meeting_creation: 'Daily Meeting Creation'
      daily_note_creation: 'Daily Note Creation'
      daily_company_creation: 'Daily Company Creation'
      daily_quote_creation: 'Daily Quote Creation'
      daily_calls_logged: 'Daily Calls Logged'
      daily_emails_sent: 'Daily Emails Sent'
      user_activity_summary: 'User Activity Summary'
      feature_usage_overview: 'Feature Usage Overview'
      active_inactive_users: 'Active vs Inactive Users'
      email_integration_usage: 'Email Integration Usage'
      calendar_integration_usage: 'Calendar Integration Usage'
      marketplace_apps_usage: 'Marketplace Apps Usage'
    metric_names:
      daily_logged_in_users: 'Logged In Users'
      daily_lead_creation: 'Leads Created'
      daily_deal_creation: 'Deals Created'
      daily_contact_creation: 'Contacts Created'
      daily_task_creation: 'Tasks Created'
      daily_meeting_creation: 'Meetings Created'
      daily_note_creation: 'Notes Created'
      daily_company_creation: 'Companies Created'
      daily_quote_creation: 'Quotes Created'
      daily_calls_logged: 'Calls Logged'
      daily_emails_sent: 'Emails Sent'
  notice:
    sign_in_your_account: 'Sign in to your account.'
    for_better_user_experience: 'For better user experience, sign in from a desktop/laptop browser.'
    email: 'Email'
    password: 'Password'
    confirm_password: 'Confirm Password'
    keep_me_logged_in: 'Keep me logged in'
    contact_us: 'Contact Us'
    privacy_policy: 'Privacy Policy'
    support: 'Support'
  password:
    forgot_your_password: 'Forgot your password ?'
    reset_link_with_instructions: "Don't worry. We'll email you a password reset link with instructions."
    update_your_password: 'Update your password'
    set_your_password: 'Please set a password so that you can log in.'
    confirm_password: 'Confirm Password'
  mailer:
    forgot_your_password: 'Forgot your password? It happens to the best of us.'
    reset_password: 'Reset Password'
    click_below_to_reset_password: 'Click below to reset your password'
    ignore_message: "If you didn't make this request, please ignore the email."
    great_day: 'Have a great day!'
    team_kylas: 'Have a great day! from team Kylas'
    notify_that_password_has_change: "We're contacting you to notify you that your password has been changed."
    user_deactivated:
      salutation: "Hello %{email}!"
      subject: "Account Deactivated"
      content: "We're contacting you to notify you that your account on Kylas Customer Success has been deactivated."
      footer: "Please contact admin at %{admin_email} to activate your account again."
    user_activated:
      salutation: "Hello %{email}!"
      subject: "Account Activated"
      content: "We're contacting you to notify you that your account on Kylas Customer Success has been Activated."
      footer: "Please contact admin at %{admin_email} for any queries."
    email_changed:
      salutation: "Hello %{email}!"
      subject: "Email Changed"
      content: "We're contacting you to notify you that your email has been changed to %{new_email}."
      footer: "Please contact admin at %{admin_email} for any details."
  form:
    field:
      name: "Full Name"
      email: "Email"
      password: "Password"
      confirm_password: "Confirm Password"
      new_password: "New Password"
    button:
      sign_out: "Sign Out"
      add_user: "Add User"
      update_user: "Edit"
      cancel: "Cancel"
      reset_password: "Reset Password"
      set_password: "Set Password"
      back_to_login: "Back to Login"
      request_reset_link: "Request Reset Link"
      forgot_password: "Forgot your password?"
      sign_in: "Sign In"
      login: "Login"
    placeholder:
      search_in_users: "Search In all Users"
  table:
    button:
      resend_invitation: "Resend Invitation"
      edit: "Edit"
      deactivate: "Deactivate"
      activate: "Activate"
    heading:
      name: "Name"
      email: "Email"
      invitation_status: "Invitation Status"
      actions: "Actions"
      id: "Id"
      category: "Category"
      status: "Status"
      description: "Description"
  account_details:
    last_updated_at: "Last Updated At:"
    last_updated_by: "Last Updated By:"
    basic_information: "Basic Information"
    account_name: "Account Name:"
    kylas_tenant_id: "Kylas Tenant ID:"
    industry: "Industry:"
    contact_details: "Contact Details"
    marketplace_apps_installed: "Marketplace apps installed"
    account_form_instructions: "Below you can set the Account Manager and Support Executive for this account"
    form:
      account_manager: "Account Manager:"
      support_executive: "Support Executive:"
      search: "Search"
      save: "Save"
      saving: "Saving..."
    system_updated_at: "System Updated At:"
    kylas_icon: "K"
    kylas_growth_engine: "Kylas Growth Engine"
    account_details: "Account Details"
    plan_details: "Plan Details"
    rgs_inputs: "RGS Inputs"
    customer_ask: "Customer Ask"
    usage_reports: "Usage Reports"
    not_found: "Account Not Found"
  account_list:
    all_kylas_accounts: "All Kylas Accounts"
    kylas_tenant_id: "Kylas Tenant ID"
    name: "Name"
    email: "Email"
    industry: "Industry"
    current_plan: "Current Plan"
    plan_start_date: "Plan Start Date"
    next_renewal: 'Next Renewal'
    sign_up_at: "Sign-Up At"
    ob_start: "OB Start"
    ob_completion: "OB Completion"
    latest_list: "%{count} items. Sorted by Sign-up At, Ascending. Updated a few seconds ago"
  pagination:
    showing_items: "Showing %{from} - %{to} of %{count} items"
    results_per_page: "Results per page"
    first: "First"
    last: "Last"

  plan_details:
    status: "Status"
    month: "/month"
    excluding_tax: "excluding taxes"
    last_paid_on: "Last Paid On"
    next_renewal: "Next Renewal"
    days_to: "5 Days to go"
    add_ons: "Add-ons"
    ob_completion: "Below you can select the Onboarding Completion Date of this account."
    last_updated_by: "Last Updated By"
    last_updated_at: "Last Updated At"
    system_updated_at: "System Updated At"
    success: "Successfully saved the data"
    error: "Error in saving the data"
    not_yet_updated: "Not yet updated"

  rgs_inputs:
    header_text: "Please insert the Requirement Gather Sheet (RGS) inputs below, so it will be easily accessible to all Kylas support portal users."
    user_onboarded_text: "Number of users to be Onboarded"
    no_of_users_field: "Total Number of users (Excluding Managers):"
    user_placeholder: "Number of users"
    manager_placeholder: "Number of managers"
    no_of_manager_field: "Total Number of Managers & Above:"
    sales_rgs_input: "Kylas Sales RGS Input"
    unverified_marketplace_apps: "Unverified Marketplace Applications (private)"
    no_unverfied_app: "No unverified apps are available"
    marketplace_rgs_input: "Kylas Marketplace RGS Input"
    success: "Successfully saved the data"
    error: "Error in saving the data"
    table:
      volume_placeholder: "Enter expected volume"
      entity: "Entity"
      critical: "Critical"
      frequency: "Frequency"
      volume: "Expected volume"
      choose: "Choose"
      radio_yes: "Yes"
      radio_no: "No"
      add: "Add"
      save: "Save"
      saving: "Saving..."
      cancel: "Cancel"
      marketplace_apps: "Marketplace Apps"
      integrated: "Integrated"

  customer_asks:
    header_text: "Below you can set customer's ask/additional requirements."
    no_requirement_found: "No Requirements Found!"
    customer_requirement:
      title: "Customer Requirement"
      add: "Add"
      adding: "Adding"
      for_label: "For *:"
      status_label: "Status *:"
      due_date_label: "Due Date *:"
      description_label: "Description *:"
      placeholder_description: "Description"
      choose: "Choose"
      cancel: "Cancel"
      add_requirement: "Add Requirement +"
      edit: "Edit"
      delete: "Delete"
      updated: "Requirement Updated Successfully"
      created: "Requirement Created Successfully"
      destroyed: "Requirement Destroyed Successfully"
      does_not_exist: "Requirement Doesnt Exist"

  chargebee:
    failed_to_retrieve: "Failed to retrieve subscription."
    error_message: "Error occurred while retrieving subscription:"
